package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.QrCodeUtils;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.domain.MerchantTable;
import com.ruoyi.system.mapper.MerchantTableMapper;
import com.ruoyi.system.service.IMerchantTableService;
import com.ruoyi.system.service.IMerchantService;

/**
 * 商家桌台管理 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class MerchantTableServiceImpl implements IMerchantTableService
{
    @Autowired
    private MerchantTableMapper merchantTableMapper;

    @Autowired
    private IMerchantService merchantService;

    /**
     * 查询桌台信息
     * 
     * @param tableId 桌台ID
     * @return 桌台信息
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public MerchantTable selectMerchantTableById(Long tableId)
    {
        return merchantTableMapper.selectMerchantTableById(tableId);
    }

    /**
     * 查询桌台列表
     * 
     * @param merchantTable 桌台信息
     * @return 桌台集合
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<MerchantTable> selectMerchantTableList(MerchantTable merchantTable)
    {
        return merchantTableMapper.selectMerchantTableList(merchantTable);
    }

    /**
     * 根据商家ID查询桌台列表
     * 
     * @param merchantId 商家ID
     * @return 桌台集合
     */
    @Override
    public List<MerchantTable> selectMerchantTablesByMerchantId(Long merchantId)
    {
        return merchantTableMapper.selectMerchantTablesByMerchantId(merchantId);
    }

    /**
     * 根据商家ID和桌台号查询桌台信息
     * 
     * @param merchantId 商家ID
     * @param tableNumber 桌台号
     * @return 桌台信息
     */
    @Override
    public MerchantTable selectMerchantTableByMerchantIdAndNumber(Long merchantId, String tableNumber)
    {
        return merchantTableMapper.selectMerchantTableByMerchantIdAndNumber(merchantId, tableNumber);
    }

    /**
     * 校验桌台号在商家内是否唯一
     * 
     * @param merchantTable 桌台信息
     * @return 结果
     */
    @Override
    public boolean checkTableNumberUnique(MerchantTable merchantTable)
    {
        Long tableId = StringUtils.isNull(merchantTable.getTableId()) ? -1L : merchantTable.getTableId();
        MerchantTable info = merchantTableMapper.checkTableNumberUnique(merchantTable);
        if (StringUtils.isNotNull(info) && info.getTableId().longValue() != tableId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 新增桌台
     * 
     * @param merchantTable 桌台信息
     * @return 结果
     */
    @Override
    public int insertMerchantTable(MerchantTable merchantTable)
    {
        merchantTable.setCreateTime(DateUtils.getNowDate());
        int result = merchantTableMapper.insertMerchantTable(merchantTable);
        
        // 生成二维码
        if (result > 0 && merchantTable.getTableId() != null)
        {
            String qrCode = generateTableQrCode(merchantTable.getTableId());
            if (StringUtils.isNotEmpty(qrCode))
            {
                merchantTable.setQrCode(qrCode);
                merchantTableMapper.updateMerchantTable(merchantTable);
            }
        }
        
        return result;
    }

    /**
     * 修改桌台
     * 
     * @param merchantTable 桌台信息
     * @return 结果
     */
    @Override
    public int updateMerchantTable(MerchantTable merchantTable)
    {
        merchantTable.setUpdateTime(DateUtils.getNowDate());
        return merchantTableMapper.updateMerchantTable(merchantTable);
    }

    /**
     * 批量删除桌台
     * 
     * @param tableIds 需要删除的桌台ID
     * @return 结果
     */
    @Override
    public int deleteMerchantTableByIds(Long[] tableIds)
    {
        return merchantTableMapper.deleteMerchantTableByIds(tableIds);
    }

    /**
     * 删除桌台信息
     * 
     * @param tableId 桌台ID
     * @return 结果
     */
    @Override
    public int deleteMerchantTableById(Long tableId)
    {
        return merchantTableMapper.deleteMerchantTableById(tableId);
    }

    /**
     * 根据商家ID删除桌台
     * 
     * @param merchantId 商家ID
     * @return 结果
     */
    @Override
    public int deleteMerchantTableByMerchantId(Long merchantId)
    {
        return merchantTableMapper.deleteMerchantTableByMerchantId(merchantId);
    }

    /**
     * 统计商家的桌台数量
     * 
     * @param merchantId 商家ID
     * @return 桌台数量
     */
    @Override
    public int countTablesByMerchantId(Long merchantId)
    {
        return merchantTableMapper.countTablesByMerchantId(merchantId);
    }

    /**
     * 生成桌台二维码
     *
     * @param tableId 桌台ID
     * @return 二维码路径
     */
    @Override
    public String generateTableQrCode(Long tableId)
    {
        try
        {
            // 查询桌台信息
            MerchantTable table = merchantTableMapper.selectMerchantTableById(tableId);
            if (table == null)
            {
                return null;
            }

            // 查询商家信息
            Merchant merchant = merchantService.selectMerchantById(table.getMerchantId());
            if (merchant == null)
            {
                return null;
            }

            // 生成二维码内容
            String qrContent = QrCodeUtils.generateTableQrContent(merchant.getMerchantCode(), table.getTableNumber());

            // 生成文件名
            String fileName = "table_" + merchant.getMerchantCode() + "_" + table.getTableNumber() + "_" + System.currentTimeMillis();

            // 生成二维码并保存
            String qrCodePath = QrCodeUtils.generateQrCode(qrContent, fileName);

            return qrCodePath;
        }
        catch (Exception e)
        {
            // 记录日志
            return null;
        }
    }

    /**
     * 批量生成桌台二维码
     *
     * @param merchantId 商家ID
     * @param updateBy 更新者
     * @return 生成成功的数量
     */
    @Override
    public int batchGenerateTableQrCode(Long merchantId, String updateBy)
    {
        try
        {
            // 查询商家下所有桌台
            List<MerchantTable> tables = merchantTableMapper.selectMerchantTablesByMerchantId(merchantId);
            if (tables == null || tables.isEmpty())
            {
                return 0;
            }

            int successCount = 0;
            for (MerchantTable table : tables)
            {
                // 生成二维码
                String qrCode = generateTableQrCode(table.getTableId());
                if (StringUtils.isNotEmpty(qrCode))
                {
                    // 更新桌台二维码
                    MerchantTable updateTable = new MerchantTable();
                    updateTable.setTableId(table.getTableId());
                    updateTable.setQrCode(qrCode);
                    updateTable.setUpdateBy(updateBy);
                    updateTable.setUpdateTime(DateUtils.getNowDate());

                    int result = merchantTableMapper.updateMerchantTable(updateTable);
                    if (result > 0)
                    {
                        successCount++;
                    }
                }
            }

            return successCount;
        }
        catch (Exception e)
        {
            // 记录日志
            return 0;
        }
    }
}
