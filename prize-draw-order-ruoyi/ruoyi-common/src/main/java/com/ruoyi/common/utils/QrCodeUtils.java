package com.ruoyi.common.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.file.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码生成工具类
 * 
 * <AUTHOR>
 */
public class QrCodeUtils
{
    private static final Logger log = LoggerFactory.getLogger(QrCodeUtils.class);

    /** 默认二维码宽度 */
    private static final int DEFAULT_WIDTH = 300;
    
    /** 默认二维码高度 */
    private static final int DEFAULT_HEIGHT = 300;
    
    /** 默认二维码格式 */
    private static final String DEFAULT_FORMAT = "PNG";
    
    /** 二维码黑色 */
    private static final int BLACK = 0xFF000000;
    
    /** 二维码白色 */
    private static final int WHITE = 0xFFFFFFFF;

    /**
     * 生成二维码并保存到文件
     * 
     * @param content 二维码内容
     * @param fileName 文件名（不包含路径）
     * @return 相对路径
     */
    public static String generateQrCode(String content, String fileName)
    {
        return generateQrCode(content, fileName, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 生成二维码并保存到文件
     * 
     * @param content 二维码内容
     * @param fileName 文件名（不包含路径）
     * @param width 二维码宽度
     * @param height 二维码高度
     * @return 相对路径
     */
    public static String generateQrCode(String content, String fileName, int width, int height)
    {
        try
        {
            // 设置二维码参数
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            hints.put(EncodeHintType.MARGIN, 1);

            // 生成二维码
            BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
            
            // 创建BufferedImage
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    image.setRGB(x, y, bitMatrix.get(x, y) ? BLACK : WHITE);
                }
            }

            // 确保文件名以.png结尾
            if (!fileName.toLowerCase().endsWith(".png"))
            {
                fileName += ".png";
            }

            // 创建二维码保存目录
            String qrCodeDir = RuoYiConfig.getUploadPath() + "/qrcode";
            File dir = new File(qrCodeDir);
            if (!dir.exists())
            {
                dir.mkdirs();
            }

            // 生成完整文件路径
            String filePath = qrCodeDir + "/" + fileName;
            File file = new File(filePath);

            // 保存图片
            ImageIO.write(image, DEFAULT_FORMAT, file);

            // 返回相对路径
            String relativePath = "/upload/qrcode/" + fileName;
            log.info("二维码生成成功: {}", relativePath);
            return relativePath;
        }
        catch (WriterException | IOException e)
        {
            log.error("生成二维码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成桌台二维码内容
     * 
     * @param merchantCode 商家编码
     * @param tableNumber 桌台号
     * @return 二维码内容
     */
    public static String generateTableQrContent(String merchantCode, String tableNumber)
    {
        // 这里可以根据实际需求调整二维码内容格式
        // 可以是URL格式、JSON格式或简单的字符串格式
        return merchantCode + "_" + tableNumber;
    }

    /**
     * 生成桌台二维码内容（URL格式）
     * 
     * @param merchantCode 商家编码
     * @param tableNumber 桌台号
     * @param baseUrl 基础URL
     * @return 二维码内容
     */
    public static String generateTableQrContentUrl(String merchantCode, String tableNumber, String baseUrl)
    {
        return baseUrl + "?merchantCode=" + merchantCode + "&tableNumber=" + tableNumber;
    }

    /**
     * 删除二维码文件
     * 
     * @param relativePath 相对路径
     * @return 是否删除成功
     */
    public static boolean deleteQrCode(String relativePath)
    {
        try
        {
            if (StringUtils.isEmpty(relativePath))
            {
                return false;
            }
            
            String fullPath = RuoYiConfig.getProfile() + relativePath;
            File file = new File(fullPath);
            if (file.exists() && file.isFile())
            {
                boolean deleted = file.delete();
                if (deleted)
                {
                    log.info("二维码文件删除成功: {}", relativePath);
                }
                return deleted;
            }
            return false;
        }
        catch (Exception e)
        {
            log.error("删除二维码文件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查二维码文件是否存在
     * 
     * @param relativePath 相对路径
     * @return 是否存在
     */
    public static boolean exists(String relativePath)
    {
        try
        {
            if (StringUtils.isEmpty(relativePath))
            {
                return false;
            }
            
            String fullPath = RuoYiConfig.getProfile() + relativePath;
            File file = new File(fullPath);
            return file.exists() && file.isFile();
        }
        catch (Exception e)
        {
            log.error("检查二维码文件是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
