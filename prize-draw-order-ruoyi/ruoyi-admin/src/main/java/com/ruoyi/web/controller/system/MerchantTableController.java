package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.MerchantDataScope;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.MerchantTable;
import com.ruoyi.system.service.IMerchantTableService;

/**
 * 商家桌台管理 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/merchant/table")
public class MerchantTableController extends BaseController
{
    @Autowired
    private IMerchantTableService merchantTableService;

    /**
     * 获取桌台列表
     */
    @PreAuthorize("@ss.hasPermi('merchant:table:query')")
    @MerchantDataScope(merchantAlias = "t")
    @GetMapping("/list")
    public TableDataInfo list(MerchantTable merchantTable)
    {
        startPage();
        List<MerchantTable> list = merchantTableService.selectMerchantTableList(merchantTable);
        return getDataTable(list);
    }

    /**
     * 根据商家ID获取桌台列表
     */
    @PreAuthorize("@ss.hasPermi('merchant:table:query')")
    @GetMapping("/merchant/{merchantId}")
    public AjaxResult getByMerchantId(@PathVariable Long merchantId)
    {
        List<MerchantTable> list = merchantTableService.selectMerchantTablesByMerchantId(merchantId);
        return success(list);
    }

    /**
     * 导出桌台列表
     */
    @Log(title = "桌台管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('merchant:table:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, MerchantTable merchantTable)
    {
        List<MerchantTable> list = merchantTableService.selectMerchantTableList(merchantTable);
        ExcelUtil<MerchantTable> util = new ExcelUtil<MerchantTable>(MerchantTable.class);
        util.exportExcel(response, list, "桌台数据");
    }

    /**
     * 根据桌台编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:table:query')")
    @GetMapping(value = "/{tableId}")
    public AjaxResult getInfo(@PathVariable Long tableId)
    {
        return success(merchantTableService.selectMerchantTableById(tableId));
    }

    /**
     * 根据商家ID和桌台号查询桌台信息
     */
    @GetMapping(value = "/merchant/{merchantId}/number/{tableNumber}")
    public AjaxResult getByMerchantIdAndNumber(@PathVariable Long merchantId, @PathVariable String tableNumber)
    {
        MerchantTable table = merchantTableService.selectMerchantTableByMerchantIdAndNumber(merchantId, tableNumber);
        return success(table);
    }

    /**
     * 新增桌台
     */
    @PreAuthorize("@ss.hasPermi('merchant:table:add')")
    @Log(title = "桌台管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MerchantTable merchantTable)
    {
        if (!merchantTableService.checkTableNumberUnique(merchantTable))
        {
            return error("新增桌台'" + merchantTable.getTableNumber() + "'失败，桌台号已存在");
        }
        merchantTable.setCreateBy(getUsername());
        return toAjax(merchantTableService.insertMerchantTable(merchantTable));
    }

    /**
     * 修改桌台
     */
    @PreAuthorize("@ss.hasPermi('merchant:table:edit')")
    @Log(title = "桌台管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MerchantTable merchantTable)
    {
        if (!merchantTableService.checkTableNumberUnique(merchantTable))
        {
            return error("修改桌台'" + merchantTable.getTableNumber() + "'失败，桌台号已存在");
        }
        merchantTable.setUpdateBy(getUsername());
        return toAjax(merchantTableService.updateMerchantTable(merchantTable));
    }

    /**
     * 删除桌台
     */
    @PreAuthorize("@ss.hasPermi('merchant:table:remove')")
    @Log(title = "桌台管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{tableIds}")
    public AjaxResult remove(@PathVariable Long[] tableIds)
    {
        return toAjax(merchantTableService.deleteMerchantTableByIds(tableIds));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('merchant:table:edit')")
    @Log(title = "桌台管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MerchantTable merchantTable)
    {
        merchantTable.setUpdateBy(getUsername());
        return toAjax(merchantTableService.updateMerchantTable(merchantTable));
    }

    /**
     * 校验桌台号
     */
    @PostMapping("/checkTableNumberUnique")
    public boolean checkTableNumberUnique(@RequestBody MerchantTable merchantTable)
    {
        return merchantTableService.checkTableNumberUnique(merchantTable);
    }

    /**
     * 生成桌台二维码
     */
    @PreAuthorize("@ss.hasPermi('merchant:table:edit')")
    @Log(title = "桌台管理", businessType = BusinessType.UPDATE)
    @PostMapping("/generateQrCode/{tableId}")
    public AjaxResult generateQrCode(@PathVariable Long tableId)
    {
        String qrCode = merchantTableService.generateTableQrCode(tableId);
        if (qrCode != null)
        {
            MerchantTable table = new MerchantTable();
            table.setTableId(tableId);
            table.setQrCode(qrCode);
            table.setUpdateBy(getUsername());
            merchantTableService.updateMerchantTable(table);
            return success(qrCode);
        }
        return error("二维码生成失败");
    }

    /**
     * 批量生成桌台二维码
     */
    @PreAuthorize("@ss.hasPermi('merchant:table:edit')")
    @Log(title = "桌台管理", businessType = BusinessType.UPDATE)
    @PostMapping("/batchGenerateQrCode/{merchantId}")
    public AjaxResult batchGenerateQrCode(@PathVariable Long merchantId)
    {
        int result = merchantTableService.batchGenerateTableQrCode(merchantId, getUsername());
        if (result > 0)
        {
            return success("批量生成成功，共生成 " + result + " 个二维码");
        }
        return error("批量生成失败");
    }

    /**
     * 统计商家桌台数量
     */
    @PreAuthorize("@ss.hasPermi('merchant:table:query')")
    @GetMapping("/count/{merchantId}")
    public AjaxResult countByMerchantId(@PathVariable Long merchantId)
    {
        int count = merchantTableService.countTablesByMerchantId(merchantId);
        return success(count);
    }
}
